'use client';

import React from 'react';
import styled from 'styled-components';
import { FileTab } from '../../App';

const StatusBarContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 24px;
  background-color: ${props => props.theme.colors.statusBar.background};
  color: ${props => props.theme.colors.statusBar.foreground};
  font-size: ${props => props.theme.typography.fontSize.xs};
  padding: 0 ${props => props.theme.spacing.md};
  user-select: none;
`;

const StatusSection = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.lg};
`;

const StatusItem = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
  cursor: default;
`;

const StatusButton = styled.button`
  background: none;
  border: none;
  color: ${props => props.theme.colors.statusBar.foreground};
  cursor: pointer;
  padding: 2px ${props => props.theme.spacing.sm};
  border-radius: ${props => props.theme.borderRadius.sm};
  font-size: ${props => props.theme.typography.fontSize.xs};

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  &:active {
    background-color: rgba(255, 255, 255, 0.2);
  }
`;

const FileInfo = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
`;

const LanguageIndicator = styled.span`
  background-color: rgba(255, 255, 255, 0.1);
  padding: 2px ${props => props.theme.spacing.sm};
  border-radius: ${props => props.theme.borderRadius.sm};
  font-weight: ${props => props.theme.typography.fontWeight.medium};
`;

const Separator = styled.div`
  width: 1px;
  height: 16px;
  background-color: rgba(255, 255, 255, 0.2);
`;

interface StatusBarProps {
  activeFile: FileTab | undefined;
  onToggleTerminal: () => void;
  isTerminalVisible: boolean;
}

const StatusBar: React.FC<StatusBarProps> = ({
  activeFile,
  onToggleTerminal,
  isTerminalVisible
}) => {
  const getLanguageDisplayName = (language: string): string => {
    const languageMap: { [key: string]: string } = {
      'javascript': 'JavaScript',
      'typescript': 'TypeScript',
      'python': 'Python',
      'html': 'HTML',
      'css': 'CSS',
      'json': 'JSON',
      'markdown': 'Markdown',
      'plaintext': 'Plain Text'
    };
    return languageMap[language] || language.charAt(0).toUpperCase() + language.slice(1);
  };

  const formatFileSize = (content: string): string => {
    const bytes = new Blob([content]).size;
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  };

  const getLineCount = (content: string): number => {
    return content.split('\n').length;
  };

  const getCurrentTime = (): string => {
    return new Date().toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <StatusBarContainer>
      <StatusSection>
        {activeFile ? (
          <FileInfo>
            <StatusItem>
              📄 {activeFile.name}
              {activeFile.isDirty && ' ●'}
            </StatusItem>

            <Separator />

            <StatusItem>
              <LanguageIndicator>
                {getLanguageDisplayName(activeFile.language)}
              </LanguageIndicator>
            </StatusItem>

            <StatusItem>
              {getLineCount(activeFile.content)} lines
            </StatusItem>

            <StatusItem>
              {formatFileSize(activeFile.content)}
            </StatusItem>

            {activeFile.path && (
              <>
                <Separator />
                <StatusItem title={activeFile.path}>
                  📁 {window.electronAPI?.dirname(activeFile.path) || ''}
                </StatusItem>
              </>
            )}
          </FileInfo>
        ) : (
          <StatusItem>
            No file open
          </StatusItem>
        )}
      </StatusSection>

      <StatusSection>
        <StatusButton
          onClick={onToggleTerminal}
          title={isTerminalVisible ? 'Hide Terminal' : 'Show Terminal'}
        >
          {isTerminalVisible ? '🔽' : '🔼'} Terminal
        </StatusButton>

        <Separator />

        <StatusItem>
          🕒 {getCurrentTime()}
        </StatusItem>

        <StatusItem>
          >CØDE
        </StatusItem>
      </StatusSection>
    </StatusBarContainer>
  );
};

export default StatusBar;
