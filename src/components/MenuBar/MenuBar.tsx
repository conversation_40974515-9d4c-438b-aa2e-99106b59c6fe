'use client';

import React from 'react';
import styled from 'styled-components';

const MenuBarContainer = styled.div`
  display: flex;
  align-items: center;
  height: 30px;
  background-color: ${props => props.theme.colors.menuBar.background};
  border-bottom: 1px solid ${props => props.theme.colors.border.primary};
  padding: 0 ${props => props.theme.spacing.md};
  font-size: ${props => props.theme.typography.fontSize.sm};
  user-select: none;
  -webkit-app-region: drag;
`;

const MenuTitle = styled.div`
  color: ${props => props.theme.colors.text.primary};
  font-weight: ${props => props.theme.typography.fontWeight.medium};
  flex: 1;
  text-align: center;
  -webkit-app-region: no-drag;
`;

const MenuButton = styled.button`
  background: none;
  border: none;
  color: ${props => props.theme.colors.text.secondary};
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  cursor: pointer;
  font-size: ${props => props.theme.typography.fontSize.sm};
  border-radius: ${props => props.theme.borderRadius.sm};
  -webkit-app-region: no-drag;

  &:hover {
    background-color: ${props => props.theme.colors.menuBar.hover};
    color: ${props => props.theme.colors.text.primary};
  }

  &:active {
    background-color: ${props => props.theme.colors.menuBar.selected};
  }
`;

const MenuSection = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing.sm};
`;

const MenuBar: React.FC = () => {
  return (
    <MenuBarContainer>
      <MenuSection>
        {/* Left side menu items could go here */}
      </MenuSection>

      <MenuTitle>
        {'>'} CØDE
      </MenuTitle>

      <MenuSection>
        {/* Right side menu items could go here */}
      </MenuSection>
    </MenuBarContainer>
  );
};

export default MenuBar;
