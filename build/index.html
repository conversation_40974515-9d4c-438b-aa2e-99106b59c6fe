<!doctype html><html lang="en"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width,initial-scale=1"><title>>CØDE</title><script>// Fix for Monaco Editor global reference
        if (typeof global === 'undefined') {
            var global = globalThis;
        }
        // Fix for process reference
        if (typeof process === 'undefined') {
            var process = { env: {} };
        }
        // Fix for require reference
        if (typeof require === 'undefined') {
            var require = function(module) {
                console.warn('require() called for:', module);
                return {};
            };
        }
        // Fix for module reference
        if (typeof module === 'undefined') {
            var module = { exports: {} };
        }
        // Fix for Buffer reference
        if (typeof Buffer === 'undefined') {
            var Buffer = {
                from: function(data) { return data; },
                isBuffer: function() { return false; }
            };
        }</script><style>body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #1e1e1e;
            color: #d4d4d4;
            overflow: hidden;
        }

        #root {
            height: 100vh;
            width: 100vw;
        }</style></head><body><div id="root"></div><script src="./bundle.js"></script></body></html>